{"version": 3, "file": "session.service.js", "sourceRoot": "", "sources": ["../../../src/session/session.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,gEAA4D;AAGrD,IAAM,cAAc,sBAApB,MAAM,cAAc;IAK1B,YAA6B,YAA0B;QAA1B,iBAAY,GAAZ,YAAY,CAAc;QAJtC,WAAM,GAAG,IAAI,eAAM,CAAC,gBAAc,CAAC,IAAI,CAAC,CAAC;IAIA,CAAC;IAEnD,MAAM,CAAC,MAAc;QAC5B,OAAO,GAAG,gBAAc,CAAC,UAAU,IAAI,MAAM,EAAE,CAAC;IACjD,CAAC;IAEO,WAAW,CAAC,MAAc;QACjC,OAAO,GAAG,gBAAc,CAAC,gBAAgB,IAAI,MAAM,EAAE,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CACnB,MAAc,EACd,SAAiB,EACjB,UAAkB;QAElB,IAAI,CAAC;YACJ,MAAM,IAAI,CAAC,YAAY;iBACrB,SAAS,EAAE;iBACX,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;YACnE,MAAM,KAAK,CAAC;QACb,CAAC;IACF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,MAAc;QAClC,IAAI,CAAC;YACJ,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;YACnE,MAAM,KAAK,CAAC;QACb,CAAC;IACF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,MAAc;QACpC,IAAI,CAAC;YACJ,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBACjD,MAAM;gBACN,KAAK;aACL,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACb,CAAC;IACF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACjB,MAAc,EACd,KAAa,EACb,UAAkB;QAElB,IAAI,CAAC;YACJ,MAAM,IAAI,CAAC,YAAY;iBACrB,SAAS,EAAE;iBACX,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBAC7C,MAAM;gBACN,KAAK;gBACL,KAAK;aACL,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACb,CAAC;IACF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,MAAc;QAChC,IAAI,CAAC;YACJ,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACb,CAAC;IACF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,MAAc;QAClC,IAAI,CAAC;YACJ,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;YACnE,MAAM,KAAK,CAAC;QACb,CAAC;IACF,CAAC;;AAxGW,wCAAc;AAEF,yBAAU,GAAG,SAAS,AAAZ,CAAa;AACvB,+BAAgB,GAAG,OAAO,AAAV,CAAW;yBAHvC,cAAc;IAD1B,IAAA,mBAAU,GAAE;qCAM+B,4BAAY;GAL3C,cAAc,CAyG1B"}