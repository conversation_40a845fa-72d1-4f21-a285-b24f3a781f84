import { UpdateUserRouteDto } from './dto/route.dto';
import { SessionService } from '../session/session.service';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { RequestWithUser } from './auth.controller';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
export declare class RouteController {
    private readonly sessionService;
    private usersRepository;
    private readonly logger;
    constructor(sessionService: SessionService, usersRepository: Repository<User>, logger: WinstonLogger);
    updateUserRoute(updateUserRouteDto: UpdateUserRouteDto, req: RequestWithUser): Promise<{
        status: boolean;
        message: string;
    }>;
}
