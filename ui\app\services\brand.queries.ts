import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { createBrand, getBrand, getBrands, getBrandsSimple } from './brands.services';

export function useCreateBrand() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: createBrand,
        onSuccess: (data) => {
            if (data.status === true) {
                // Invalidate queries to reload the brands data
                queryClient.invalidateQueries({ queryKey: ['brands'] });
            }
        },
        onError: (error) => {
            console.error('Error creating brand:', error);
        },
    });
}


export function useGetAllBrands(
    page: number = 1,
    limit: number = 10,
    orderBy: string = 'DESC'
) {
    return useQuery({
        queryKey: ['brands', page, limit, orderBy],
        queryFn: () => getBrands(page, limit, orderBy),
    });
}

// Backward compatibility hook for non-paginated calls
export function useGetAllBrandsSimple() {
    return useQuery({
        queryKey: ['brands-simple'],
        queryFn: () => getBrandsSimple(),
    });
}

export function useGetBrand(id:string) {
    return useQuery({
        queryFn:()=> getBrand(id),
        queryKey: ['brands', id],
    });
}
