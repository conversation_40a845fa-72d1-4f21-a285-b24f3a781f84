"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SessionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionService = void 0;
const common_1 = require("@nestjs/common");
const redis_service_1 = require("../utils/redis/redis.service");
let SessionService = SessionService_1 = class SessionService {
    constructor(redisService) {
        this.redisService = redisService;
        this.logger = new common_1.Logger(SessionService_1.name);
    }
    getKey(userId) {
        return `${SessionService_1.KEY_PREFIX}:${userId}`;
    }
    getRouteKey(userId) {
        return `${SessionService_1.ROUTE_KEY_PREFIX}:${userId}`;
    }
    /**
     * Persist the active sessionId for the user with a TTL (seconds).
     */
    async setUserSession(userId, sessionId, ttlSeconds) {
        try {
            await this.redisService
                .getClient()
                .set(this.getKey(userId), sessionId, 'EX', ttlSeconds);
        }
        catch (error) {
            this.logger.error('Failed to set user session', { userId, error });
            throw error;
        }
    }
    /**
     * Retrieve the current sessionId stored for the user (if any).
     */
    async getUserSession(userId) {
        try {
            return await this.redisService.get(this.getKey(userId));
        }
        catch (error) {
            this.logger.error('Failed to get user session', { userId, error });
            throw error;
        }
    }
    /**
     * Clear the stored session for the user (used on logout or account disable).
     */
    async clearUserSession(userId) {
        try {
            await this.redisService.getClient().del(this.getKey(userId));
        }
        catch (error) {
            this.logger.error('Failed to clear user session', {
                userId,
                error
            });
            throw error;
        }
    }
    /**
     * Store the user's current route in Redis with a TTL (seconds).
     */
    async setUserRoute(userId, route, ttlSeconds) {
        try {
            await this.redisService
                .getClient()
                .set(this.getRouteKey(userId), route, 'EX', ttlSeconds);
        }
        catch (error) {
            this.logger.error('Failed to set user route', {
                userId,
                route,
                error
            });
            throw error;
        }
    }
    /**
     * Retrieve the current route stored for the user (if any).
     */
    async getUserRoute(userId) {
        try {
            return await this.redisService.get(this.getRouteKey(userId));
        }
        catch (error) {
            this.logger.error('Failed to get user route', { userId, error });
            throw error;
        }
    }
    /**
     * Clear the stored route for the user.
     */
    async clearUserRoute(userId) {
        try {
            await this.redisService.getClient().del(this.getRouteKey(userId));
        }
        catch (error) {
            this.logger.error('Failed to clear user route', { userId, error });
            throw error;
        }
    }
};
exports.SessionService = SessionService;
SessionService.KEY_PREFIX = 'session';
SessionService.ROUTE_KEY_PREFIX = 'route';
exports.SessionService = SessionService = SessionService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [redis_service_1.RedisService])
], SessionService);
//# sourceMappingURL=session.service.js.map