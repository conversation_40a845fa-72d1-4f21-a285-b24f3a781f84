import { Role } from '../../roles/entities/role.entity';
import { Task } from '../../tasks/entities/tasks.entity';
import { ClinicUser } from '../../clinics/entities/clinic-user.entity';
import { Brand } from '../../brands/entities/brand.entity';
export declare class User {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    pin: string;
    roleId: string;
    role: Role;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    createdByUser: User;
    updatedBy: string;
    updatedByUser: User;
    mobileNumber: string;
    countryCode: string;
    working_hours: any;
    digitalSignature: string;
    registered: boolean;
    tasks: Task[];
    clinicUsers: ClinicUser[];
    brandId: string;
    brand: Brand;
    licenseNumber?: string;
    alternateMobileNumber: string;
    alternateCountryCode: string;
    lastRoute?: string;
}
