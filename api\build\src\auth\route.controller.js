"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RouteController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("./guards/jwt-auth.guard");
const route_dto_1 = require("./dto/route.dto");
const session_service_1 = require("../session/session.service");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../users/entities/user.entity");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
let RouteController = class RouteController {
    constructor(sessionService, usersRepository, logger) {
        this.sessionService = sessionService;
        this.usersRepository = usersRepository;
        this.logger = logger;
    }
    async updateUserRoute(updateUserRouteDto, req) {
        try {
            const userId = req.user.id;
            const { route } = updateUserRouteDto;
            // Database first: Store route in database for persistence
            await this.usersRepository.update({ id: userId }, { lastRoute: route });
            // Then cache: Store route in Redis for fast access (24 hours TTL)
            await this.sessionService.setUserRoute(userId, route, 60 * 60 * 24);
            return {
                status: true,
                message: 'Route updated successfully'
            };
        }
        catch (error) {
            this.logger.error(`Failed to update user route for user ${req.user.id}`, error);
            throw new common_1.InternalServerErrorException('Failed to update user route');
        }
    }
};
exports.RouteController = RouteController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: "Update user's current route" }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Route updated successfully'
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Internal server error' }),
    (0, track_method_decorator_1.TrackMethod)('updateUserRoute'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [route_dto_1.UpdateUserRouteDto, Object]),
    __metadata("design:returntype", Promise)
], RouteController.prototype, "updateUserRoute", null);
exports.RouteController = RouteController = __decorate([
    (0, swagger_1.ApiTags)('Route'),
    (0, common_1.Controller)('auth/user-route'),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [session_service_1.SessionService,
        typeorm_2.Repository,
        winston_logger_service_1.WinstonLogger])
], RouteController);
//# sourceMappingURL=route.controller.js.map