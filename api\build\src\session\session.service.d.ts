import { RedisService } from '../utils/redis/redis.service';
export declare class SessionService {
    private readonly redisService;
    private readonly logger;
    private static readonly KEY_PREFIX;
    private static readonly ROUTE_KEY_PREFIX;
    constructor(redisService: RedisService);
    private getKey;
    private getRouteKey;
    /**
     * Persist the active sessionId for the user with a TTL (seconds).
     */
    setUserSession(userId: string, sessionId: string, ttlSeconds: number): Promise<void>;
    /**
     * Retrieve the current sessionId stored for the user (if any).
     */
    getUserSession(userId: string): Promise<string | null>;
    /**
     * Clear the stored session for the user (used on logout or account disable).
     */
    clearUserSession(userId: string): Promise<void>;
    /**
     * Store the user's current route in Redis with a TTL (seconds).
     */
    setUserRoute(userId: string, route: string, ttlSeconds: number): Promise<void>;
    /**
     * Retrieve the current route stored for the user (if any).
     */
    getUserRoute(userId: string): Promise<string | null>;
    /**
     * Clear the stored route for the user.
     */
    clearUserRoute(userId: string): Promise<void>;
}
