"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const users_service_1 = require("../users/users.service");
const role_service_1 = require("../roles/role.service");
const user_entity_1 = require("../users/entities/user.entity");
const bcrypt = require("bcrypt");
const role_enum_1 = require("../roles/role.enum");
const user_otps_service_1 = require("../user-otps/user-otps.service");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const send_mail_service_1 = require("../utils/aws/ses/send-mail-service");
const constants_1 = require("../utils/constants");
const get_login_url_1 = require("../utils/common/get-login-url");
const uuid_1 = require("uuid");
const session_service_1 = require("../session/session.service");
let AuthService = class AuthService {
    constructor(usersService, jwtService, roleService, userOtpsService, userService, usersRepository, mailService, sessionService) {
        this.usersService = usersService;
        this.jwtService = jwtService;
        this.roleService = roleService;
        this.userOtpsService = userOtpsService;
        this.userService = userService;
        this.usersRepository = usersRepository;
        this.mailService = mailService;
        this.sessionService = sessionService;
    }
    async loginByEmail(validateEmailDto) {
        const { email, otp } = validateEmailDto;
        const { token, roleName } = await this.userOtpsService.validateOtp({
            email,
            otp
        });
        return { token, roleName };
    }
    async verifyOtp(verifyOtpDto) {
        const user = await this.usersService.findOneByEmail(verifyOtpDto.email);
        if (!user) {
            throw new common_1.UnauthorizedException('User not found');
        }
        const userRole = await this.roleService.findById(user.roleId);
        if (userRole.name !== role_enum_1.Role.SUPER_ADMIN &&
            userRole.name !== role_enum_1.Role.ADMIN) {
            throw new common_1.UnauthorizedException('OTP verification is only for admin users');
        }
        // TODO: Verify OTP
        const payload = {
            email: user.email,
            sub: user.id,
            roleId: user.roleId
        };
        return {
            access_token: this.jwtService.sign(payload)
        };
    }
    async loginPin(pinLoginDto) {
        var _a, _b, _c;
        try {
            const users = await this.usersService.findAll(pinLoginDto.brandId);
            let matchedUser;
            for (const user of users) {
                if (user.pin) {
                    const isPinValid = await bcrypt.compare(pinLoginDto.pin, user.pin);
                    if (isPinValid) {
                        matchedUser = user;
                        break;
                    }
                }
            }
            if (!matchedUser) {
                throw new common_1.UnauthorizedException('The PIN is invalid');
            }
            const role = await this.roleService.findById(matchedUser.roleId);
            const isFirstLogin = !matchedUser.registered;
            // Generate JWT token
            const clinicUsers = await this.usersService.findByUserId(matchedUser.id);
            const isMultiClinic = clinicUsers.length > 1; // make it > 1
            const isFullyOnboarded = clinicUsers.every(cu => cu.isOnboarded);
            // Find the clinic that matches the brandId from the login request
            const selectedClinic = clinicUsers.find(cu => cu.brandId === pinLoginDto.brandId) ||
                null;
            // If no matching clinic was found, throw an exception
            if (!selectedClinic) {
                throw new common_1.UnauthorizedException('User not associated with the provided brand');
            }
            // Check if the clinic is active and not soft-deleted
            if (selectedClinic.clinic.deletedAt) {
                throw new common_1.UnauthorizedException('This clinic is no longer available. Please contact your administrator.');
            }
            if (!selectedClinic.clinic.isActive) {
                throw new common_1.UnauthorizedException('This clinic is currently inactive. Please contact your administrator.');
            }
            const sessionId = (0, uuid_1.v4)();
            const payload = {
                sub: matchedUser === null || matchedUser === void 0 ? void 0 : matchedUser.id,
                email: matchedUser === null || matchedUser === void 0 ? void 0 : matchedUser.email,
                role: role === null || role === void 0 ? void 0 : role.name,
                clinicId: (_a = selectedClinic === null || selectedClinic === void 0 ? void 0 : selectedClinic.clinic) === null || _a === void 0 ? void 0 : _a.id,
                brandId: pinLoginDto.brandId,
                sid: sessionId
            };
            const token = this.jwtService.sign(payload);
            // Persist session in Redis (1 day = 86400s)
            await this.sessionService.setUserSession(matchedUser.id, sessionId, 60 * 60 * 24);
            return {
                token,
                userId: selectedClinic === null || selectedClinic === void 0 ? void 0 : selectedClinic.id, // clinic_user_id
                isFirstLogin,
                isMultiClinic,
                isFullyOnboarded,
                clinicName: (_b = selectedClinic === null || selectedClinic === void 0 ? void 0 : selectedClinic.clinic) === null || _b === void 0 ? void 0 : _b.name,
                clinicId: (_c = selectedClinic === null || selectedClinic === void 0 ? void 0 : selectedClinic.clinic) === null || _c === void 0 ? void 0 : _c.id,
                role: role.name,
                brandId: selectedClinic === null || selectedClinic === void 0 ? void 0 : selectedClinic.brandId,
                username: matchedUser.firstName,
                globalUserId: matchedUser.id,
                brandName: selectedClinic.clinic.brand.name,
                lastRoute: matchedUser.lastRoute || null
            };
        }
        catch (error) {
            if (error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to login');
        }
    }
    async resetPin(email) {
        const user = await this.usersRepository.findOne({ where: { email } });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const newPin = await this.userService.generateUniquePin();
        user.pin = await bcrypt.hash(newPin, 10);
        try {
            await this.usersRepository.save(user);
            // await this.sendPinResetEmail(email, newPin);
            const subject = 'Reset login pin';
            const body = `Dear User,
            Your PIN has been reset as requested. 
            Your new PIN is: ${newPin}
			`;
            console.log(`Mock email sent to ${email} with new PIN: ${newPin}`);
            if ((0, get_login_url_1.isProduction)() && email) {
                await this.mailService.sendMail({
                    body,
                    subject,
                    toMailAddress: email
                });
            }
            else if (!(0, get_login_url_1.isProduction)()) {
                await this.mailService.sendMail({
                    body,
                    subject,
                    toMailAddress: constants_1.DEV_SES_EMAIL //email
                });
            }
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to reset PIN');
        }
    }
    async verifyPin(pin, userId) {
        try {
            // Find the user by ID
            const user = await this.usersRepository.findOne({
                where: { id: userId }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (!user.pin) {
                throw new common_1.UnauthorizedException('No PIN set for this user');
            }
            // Verify the PIN
            const isPinValid = await bcrypt.compare(pin, user.pin);
            if (!isPinValid) {
                throw new common_1.UnauthorizedException('Invalid PIN');
            }
            return {
                success: true,
                message: 'PIN verified successfully'
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to verify PIN');
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(5, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [users_service_1.UsersService,
        jwt_1.JwtService,
        role_service_1.RoleService,
        user_otps_service_1.UserOtpsService,
        users_service_1.UsersService,
        typeorm_2.Repository,
        send_mail_service_1.SESMailService,
        session_service_1.SessionService])
], AuthService);
//# sourceMappingURL=auth.service.js.map