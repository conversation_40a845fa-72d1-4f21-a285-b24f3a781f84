"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthModule = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
const config_1 = require("@nestjs/config");
const auth_controller_1 = require("./auth.controller");
const route_controller_1 = require("./route.controller");
const auth_service_1 = require("./auth.service");
const jwt_auth_strategy_1 = require("./strategy/jwt-auth.strategy");
const users_module_1 = require("../users/users.module");
const roles_guard_1 = require("./guards/roles.guard");
const role_module_1 = require("../roles/role.module");
const user_otps_module_1 = require("../user-otps/user-otps.module");
const user_entity_1 = require("../users/entities/user.entity");
const typeorm_1 = require("@nestjs/typeorm");
const ses_module_1 = require("../utils/aws/ses/ses.module");
const session_module_1 = require("../session/session.module");
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([user_entity_1.User]),
            users_module_1.UsersModule,
            passport_1.PassportModule,
            role_module_1.RoleModule,
            user_otps_module_1.UserOtpModule,
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    secret: configService.get('JWT_SECRET'),
                    signOptions: { expiresIn: '1d' }
                }),
                inject: [config_1.ConfigService]
            }),
            config_1.ConfigModule,
            user_otps_module_1.UserOtpModule,
            ses_module_1.SESModule,
            session_module_1.SessionModule
        ],
        controllers: [auth_controller_1.AuthController, route_controller_1.RouteController],
        providers: [auth_service_1.AuthService, jwt_auth_strategy_1.JwtStrategy, roles_guard_1.RolesGuard],
        exports: [auth_service_1.AuthService]
    })
], AuthModule);
//# sourceMappingURL=auth.module.js.map